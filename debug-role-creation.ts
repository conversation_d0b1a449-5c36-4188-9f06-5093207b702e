#!/usr/bin/env tsx

/**
 * Debug Role Creation
 * 
 * This script helps debug the role creation issues
 */

import { db } from './src/lib/db';

async function debugRoleCreation() {
  try {
    console.log('🔍 Debugging role creation...');

    // Check if CustomRole table exists and what fields it has
    console.log('\n📋 Checking CustomRole model...');
    
    // Try to create a simple role
    console.log('\n🧪 Attempting to create a test role...');
    
    try {
      const testRole = await db.customRole.create({
        data: {
          name: 'TEST_ROLE',
          description: 'Test role for debugging',
          isSystemRole: true,
        },
      });
      console.log('✅ Test role created successfully:', testRole);
      
      // Clean up
      await db.customRole.delete({
        where: { id: testRole.id },
      });
      console.log('🧹 Test role cleaned up');
      
    } catch (error) {
      console.log('❌ Error creating test role:', error);
      
      // Check if it's a validation error
      if (error instanceof Error) {
        console.log('Error name:', error.name);
        console.log('Error message:', error.message);
        
        // If it's a Prisma validation error, let's see what fields are required
        if (error.name === 'PrismaClientValidationError') {
          console.log('\n🔍 This is a Prisma validation error. Let\'s check the schema...');
          
          // Try to find existing roles to see the structure
          const existingRoles = await db.customRole.findMany({
            take: 1,
          });
          
          if (existingRoles.length > 0) {
            console.log('📋 Example existing role structure:', existingRoles[0]);
          } else {
            console.log('📋 No existing roles found');
          }
        }
      }
    }

    // Check permissions table
    console.log('\n📋 Checking permissions...');
    const permissionCount = await db.permission.count();
    console.log(`Found ${permissionCount} permissions`);
    
    if (permissionCount > 0) {
      const samplePermissions = await db.permission.findMany({
        take: 3,
        select: { id: true, name: true, displayName: true },
      });
      console.log('Sample permissions:', samplePermissions);
    }

    // Check if there are any existing custom roles
    console.log('\n📋 Checking existing custom roles...');
    const roleCount = await db.customRole.count();
    console.log(`Found ${roleCount} custom roles`);
    
    if (roleCount > 0) {
      const sampleRoles = await db.customRole.findMany({
        take: 3,
        select: { id: true, name: true, description: true, isSystemRole: true, organizationId: true },
      });
      console.log('Sample roles:', sampleRoles);
    }

  } catch (error) {
    console.error('❌ Debug script failed:', error);
  } finally {
    await db.$disconnect();
  }
}

// Run the debug script
debugRoleCreation().catch(console.error);
